<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Minimum-Area Rectangle</Name>
  <Location>/Engine/Source/Runtime/Engine/Private/KismetMathLibrary.cpp</Location>
  <Date>2016-06-10T17:34:31.6827923-04:00</Date>
  <Function>The algorithm finds the minimum area rectangle that encompasses a convex polygon</Function>
  <Justification> I added the function to help <PERSON> in his “Paintbrush” ability prototype for Orion</Justification>
  <Eula>http://www.geometrictools.com/Documentation/MinimumAreaRectangle.pdf</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>