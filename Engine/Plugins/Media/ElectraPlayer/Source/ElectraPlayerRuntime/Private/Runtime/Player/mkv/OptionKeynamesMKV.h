// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "PlayerCore.h"

namespace Electra
{
namespace MKV
{

const FName OptionKeyMKVLoadConnectTimeout(TEXT("mkv_connection_timeout"));				//!< (FTimeValue) value specifying connection timeout fetching the MKV
const FName OptionKeyMKVLoadNoDataTimeout(TEXT("mkv_nodata_timeout"));					//!< (FTimeValue) value specifying no-data timeout fetching the MKV

}

} // namespace Electra


