{"FileVersion": 1, "Version": 1, "VersionName": "1.0", "FriendlyName": "Electra Codecs", "Description": "Codecs for use with Electra player.", "Category": "Media Players", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/Engine/MediaFramework/Overview", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "ElectraDecoders", "Type": "RuntimeNoCommandlet", "LoadingPhase": "PreLoadingScreen", "PlatformAllowList": ["Win64", "Android", "<PERSON>", "IOS", "TVOS", "Linux"], "TargetDenyList": ["Server"]}, {"Name": "ElectraCodecFactory", "Type": "RuntimeNoCommandlet", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64", "Android", "<PERSON>", "IOS", "TVOS", "Linux"], "TargetDenyList": ["Server"]}, {"Name": "ElectraCodecFactory", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"], "TargetDenyList": ["Server"]}], "Plugins": [{"Name": "ElectraUtil", "Enabled": true}]}