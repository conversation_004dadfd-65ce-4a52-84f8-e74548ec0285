{"FileVersion": 3, "FriendlyName": "Multi-server Replication", "Version": 1, "VersionName": "1.0", "Description": "Code to help facilitate connecting multiple UE server processes to each other.", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "EnabledByDefault": false, "CanContainContent": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "MultiServerReplication", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MultiServerConfiguration", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "OnlineSubsystemUtils", "Enabled": true}]}