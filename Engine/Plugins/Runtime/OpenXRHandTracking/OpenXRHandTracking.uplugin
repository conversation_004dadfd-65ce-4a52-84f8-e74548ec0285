{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "OpenXRHandTracking", "Description": "OpenXR Hand Tracking provides XR_EXT_hand_tracking support.", "Category": "Virtual Reality", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux", "Android"], "Modules": [{"Name": "OpenXRHandTracking", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "Linux", "Android", "<PERSON>"]}, {"Name": "OpenXRHandTrackingEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}], "Plugins": [{"Name": "OpenXR", "Enabled": true, "PlatformAllowList": ["Win64", "Linux", "Android", "<PERSON>"]}, {"Name": "LiveLink", "Enabled": true}]}