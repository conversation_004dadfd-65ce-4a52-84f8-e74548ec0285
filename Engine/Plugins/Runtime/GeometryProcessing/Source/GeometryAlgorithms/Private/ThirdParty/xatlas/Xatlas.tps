<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>xatlas</Name>
  <!-- Software Name and Version  -->
<!-- Software Name:xatlas
    Version:  -->
<!-- Notes:
 -->
  <Location>Engine\Plugins\Runtime\GeometryProcessing\Source\GeometryAlgorithms\Private\ThirdParty\xatlas</Location>
  <Function>Generates UV coordinates for meshes</Function>
  <Eula>https://github.com/jpcy/xatlas/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
     <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 