-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/fallbackMaterialNetwork.glslfx

---
--- The fallback shader is used as a replacement shader if no material binding
--- was provided. It needs to define both the surfaceShader() and
--- displacementShader() terminals.
---
-- configuration
{
    "techniques": {
        "default": {
            "displacementShader": {
                "source": [ "Fallback.Displacement" ]
            },
            "surfaceShader": {
                "source": [ "Fallback.Surface" ]
            }
        }
    }
}

--- --------------------------------------------------------------------------
-- glsl Fallback.Surface

vec4 surfaceShader(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    // lighting
    color.rgb = FallbackLighting(Peye.xyz, Neye, color.rgb);
    return color;
}
--- --------------------------------------------------------------------------
-- glsl Fallback.Displacement

vec4 displacementShader(int index, vec4 Peye, vec3 Neye, vec4 patchCoord)
{
    return Peye;
}
