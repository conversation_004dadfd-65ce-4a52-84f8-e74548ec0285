# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdSemanticsLabelsAPI": {
                        "alias": {
                            "UsdSchemaBase": "SemanticsLabelsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "SemanticsLabelsAPI", 
                        "schemaKind": "multipleApplyAPI"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdSemantics.dylib", 
            "Name": "usdSemantics", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
