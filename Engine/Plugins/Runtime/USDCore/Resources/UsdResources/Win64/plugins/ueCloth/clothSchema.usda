#usda 1.0
(
    """ This file describes the Unreal Engine's custom USD schemas.
    """
    subLayers = [
        @usd/schema.usda@
    ]
)

over "GLOBAL" (
    customData = {
        string libraryName       = "unreal"
        string libraryPrefix     = "unreal"
        bool skipCodeGeneration  = true
    }
)
{
}

class "ClothRootAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["Scope"]
    }
    doc = """Apply to the prim that is the root of a garment"""
)
{
}

class "ClothSolverAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "multipleApply"
        token[] apiSchemaAllowedInstanceNames = ["chaos", "clo"]
        token propertyNamespacePrefix  = "solver"
    }
    doc = """The ClothSolverAPI defines a relationship between a garment and a cloth solver properties.
    Note that a garment can have multiple solvers associated with it.
    """
)
{
    # will be instantiated as solver:<instance name>:properties
    rel properties (
        doc = """Relationship to the solver properties prim."""
    )
}

class "ChaosSolverPropertiesAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
    }
    doc = """Apply to the prim that holds the Chaos solver properties."""
)
{
    uint numIterations = 3 (
        doc = """ """
    )
    uint numSubsteps = 6 (
        doc = """ """
    )
}

class "CloSolverPropertiesAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
    }
    doc = """Apply to the prim that holds the CLO solver properties."""
)
{
    float airDamping = 1
    bool avatarClothDetection_EdgeEdge = 0
    bool avatarClothDetection_TriangleVertex = 1
    float3 gravity = (0, -9800, 0)
    bool intersectionResolution = 1
    bool layerBasedCollisionDetection = 1
    bool proximityDetection_EdgeEdge = 1
    bool proximityDetection_VertexTriangle = 1
    float selfCollisionDetection_AvoidanceStiffness = 0.001
    bool selfCollisionDetection_EdgeEdge = 1
    bool selfCollisionDetection_TriangleVertex = 1
    uint subStepCount = 1
    float timeStep = 0.033333
}

class "SimMeshDataAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["Mesh"]
   }
    doc = """Apply to a mesh prim that holds the sim mesh data."""
)
{
    uniform token purpose = "guide" (
        doc = """Set to guide as the sim mesh prim is not intended for rendering."""
    )
    float2 restPositionScale = (1.0, 1.0) (
        doc = """ """
    )
}

class "ChaosFabricAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["Mesh"]
   }
    doc = """Apply to a sim mesh prim that holds the Chaos fabric data."""
)
{
    float[] primvars:chaos:densities (
        doc = """."""
    )
    float[] primvars:chaos:edgeStiffnesses (
        doc = """."""
    )
    float[] primvars:chaos:bendingStiffnesses (
        doc = """."""
    )
    float[] primvars:chaos:bucklingRatios (
        doc = """."""
    )
    float[] primvars:chaos:bucklingStiffnesses (
        doc = """."""
    )
    float[] primvars:chaos:flatnessRatios (
        doc = """."""
    )
    float[] primvars:chaos:areaStiffnesses (
        doc = """."""
    )
    float[] primvars:chaos:collisionThicknesses (
        doc = """."""
    )
    float[] primvars:chaos:frictionCoefficients (
        doc = """."""
    )
    float[] primvars:chaos:drags (
        doc = """."""
    )
    float[] primvars:chaos:lifts (
        doc = """."""
    )
    float[] primvars:chaos:pressures (
        doc = """."""
    )
}

class "CloFabricAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["Mesh"]
   }
    doc = """Apply to a sim mesh prim that holds the CLO fabric data."""
)
{
    # Pattern attributes
    float[] primvars:clo:bendingBiasLeft (
        doc = """."""
    )
    float[] primvars:clo:bendingBiasRight (
        doc = """."""
    )
    float[] primvars:clo:bendingWarp (
        doc = """."""
    )
    float[] primvars:clo:bendingWeft (
        doc = """."""
    )
    float[] primvars:clo:bucklingRatioBiasLeft (
        doc = """."""
    )
    float[] primvars:clo:bucklingRatioBiasRight (
        doc = """."""
    )
    float[] primvars:clo:bucklingRatioWarp (
        doc = """."""
    )
    float[] primvars:clo:bucklingRatioWeft (
        doc = """."""
    )
    float[] primvars:clo:bucklingStiffnessBiasLeft (
        doc = """."""
    )
    float[] primvars:clo:bucklingStiffnessBiasRight (
        doc = """."""
    )
    float[] primvars:clo:bucklingStiffnessWarp (
        doc = """."""
    )
    float[] primvars:clo:bucklingStiffnessWeft (
        doc = """."""
    )
    float[] primvars:clo:density (
        doc = """."""
    )
    float[] primvars:clo:friction (
        doc = """."""
    )
    float[] primvars:clo:internalDamping (
        doc = """."""
    )
    float[] primvars:clo:shearLeft (
        doc = """."""
    )
    float[] primvars:clo:shearRight (
        doc = """."""
    )
    float2[] primvars:clo:shrinkage (
        doc = """."""
    )
    float[] primvars:clo:stretchWarp (
        doc = """."""
    )
    float[] primvars:clo:stretchWeft (
        doc = """."""
    )
    float[] primvars:clo:thickness (
        doc = """."""
    )

    # Bending Wings attributes
    float[] primvars:clo:bendingStiffness (
        doc = """."""
    )
    float[] primvars:clo:bendingStiffnessScale (
        doc = """."""
    )
    bool[] primvars:clo:bendingWingInversed (
        doc = """."""
    )
    float[] primvars:clo:bucklingRatio (
        doc = """."""
    )
    float[] primvars:clo:bucklingScale (
        doc = """."""
    )

    # Springs attributes
    float[] primvars:clo:damp (
        doc = """."""
    )
    float[] primvars:clo:stiffness (
        doc = """."""
    )
}

class "RenderPatternAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the GeomSubset of a render mesh prim that holds the pattern data."""
)
{
    uniform token familyName = "pattern" (
        doc = """Define the subset as a pattern."""
    )

    rel simPattern (
        doc = """Relationship to its associated sim pattern."""
    )
}

class "SimPatternAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the GeomSubset of a sim mesh prim that holds the pattern data.
    The number of patterns in the sim mesh must match the number of patterns on the render mesh."""
)
{
    uniform token familyName = "pattern" (
        doc = """Define the subset as part of the sim mesh patterns."""
    )
}

class "SewingAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the geom subset of a sim mesh prim that holds the sewings data.
    The sewing data is the set of edges that sew pattern A to pattern B."""
)
{
    uniform token elementType = "edge" (
        doc = """Sewings are defined as edges in the sim mesh."""
    )

    uniform token familyName = "sewing" (
        doc = """Define the subset as sewings."""
    )
    
    rel patternA (
        doc = """Relationship to the first pattern prim"""
    )

    rel patternB (
        doc = """Relationship to the second pattern prim"""
    )
}

class "BendingWingAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the geom subset of a sim mesh prim that holds the bending wing data."""
)
{
    uniform token elementType = "edge" (
        doc = """Pair of edge vertex index opposite the two triangles' seam (cross edges)."""
    )

    uniform token familyName = "bendingWing" (
        doc = """Define the subset as bending wings."""
    )

    int2[] faces (
        doc = """Pair of triangle face index on each side of the seam edges containing the matching cross edge indices. The faces are assumed to be triangles when reconstructing the seam edges."""
    )

    float[] primvars:bendingAngle (
        doc = """Bending angle in degrees. 0 = Flat, positive values fold away from the edge's normal."""
    )

    # The following attributes must be defined in the CloFabricAPI but are only relevant when set within this GeomSubset API:
    #  float[] primvars:clo:bendingStiffness
    #  float[] primvars:clo:bendingStiffnessScale
    #  float[] primvars:clo:bendingWingInversed
    #  float[] primvars:clo:bucklingRatio
    #  float[] primvars:clo:bucklingScale
}

class "SpringAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the geom subset of a sim mesh prim that holds the spring data."""
)
{
    uniform token elementType = "edge" (
        doc = """Springs are defined as edges in the sim mesh."""
    )

    uniform token familyName = "spring" (
        doc = """Define the subset as springs."""
    )

    float[] primvars:restLength (
        doc = """The spring rest length."""
    )

    # The following attributes must be defined in the CloFabricAPI but are only relevant when set within this GeomSubset API:
    #  float[] primvars:clo:damp
    #  float[] primvars:clo:stiffness
}

class "TackSpringAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the geom subset of a sim mesh prim that holds the tack spring data."""
)
{
    uniform token elementType = "edge" (
        doc = """Tack springs are defined as edges in the sim mesh from free-floating vertices."""
    )

    uniform token familyName = "tackSpring" (
        doc = """Define the subset as tack springs."""
    )

    int[] faces (
        doc = """The indices of each face that supports the matching edge vertex. The face are assumed to be triangles with the associated barycentric coordinates."""
    )

    float3[] barycentricCoords (
        doc = """A pair of barycentric coordinates that describes the position of each edge vertex within their respective triangle."""
    )

    float[] primvars:restLength (
        doc = """The tack spring rest length."""
    )
}

class "PinAPI" (
    inherits = </APISchemaBase>
    customData = {
        token apiSchemaType = "singleApply"
        token[] apiSchemaCanOnlyApplyTo = ["GeomSubset"]
   }
    doc = """Apply to the geom subset of a sim mesh prim that holds the pin data."""
)
{
    uniform token elementType = "point" (
        doc = """Pins are defined as points in the sim mesh."""
    )

    uniform token familyName = "pin" (
        doc = """Define the subset as pins."""
    )
}
