{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Camera Calibration Machine Learning", "Description": "Reference implementation of a machine learning approach to distortion calibration.", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Hidden": false, "Installed": false, "Modules": [{"Name": "CameraCalibrationML", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "CameraCalibration", "Enabled": true}, {"Name": "PythonFoundationPackages", "Enabled": true}]}