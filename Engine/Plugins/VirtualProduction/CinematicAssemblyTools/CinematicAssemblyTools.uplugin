{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Cinematic Assembly Tools", "Description": "Cinematic pipeline tools for shot management and linear content creation", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Hidden": false, "Installed": false, "Modules": [{"Name": "CineAssemblyTools", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "CineAssemblyToolsEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "LevelSequenceEditor", "Enabled": true}, {"Name": "DirectoryPlaceholder", "Enabled": true}, {"Name": "MovieRenderPipeline", "Enabled": true}, {"Name": "NamingTokens", "Enabled": true}, {"Name": "Takes", "Enabled": true}]}