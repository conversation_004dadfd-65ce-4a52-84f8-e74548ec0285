// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "dnacalib/dna/DNA.h"

namespace dnac {

using dna::ActivationFunction;
using dna::Archetype;
using dna::Gender;
using dna::TranslationUnit;
using dna::RotationUnit;
using dna::Direction;
using dna::CoordinateSystem;
using dna::MeshBlendShapeChannelMapping;
using dna::Position;
using dna::TextureCoordinate;
using dna::Normal;
using dna::VertexLayout;
using dna::Delta;
using dna::TranslationRepresentation;
using dna::RotationRepresentation;
using dna::ScaleRepresentation;
using dna::RBFSolverType;
using dna::RBFFunctionType;
using dna::RBFDistanceMethod;
using dna::RBFNormalizeMethod;
using dna::AutomaticRadius;
using dna::TwistAxis;

}  // namespace dnac
