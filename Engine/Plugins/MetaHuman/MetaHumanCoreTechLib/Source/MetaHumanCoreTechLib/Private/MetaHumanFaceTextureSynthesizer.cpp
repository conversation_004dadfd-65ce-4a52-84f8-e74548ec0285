// Copyright Epic Games, Inc. All Rights Reserved.

#include "MetaHumanFaceTextureSynthesizer.h"
#include "MetaHumanCoreTechLibGlobals.h"
#include "MetaHumanTextureSynthesisModelData.h"

#include "Containers/AnsiString.h"
#include "Misc/FileHelper.h"
#include "Serialization/JsonSerializer.h"
#include "Logging/StructuredLog.h"
#include "Misc/Paths.h"

#include <ts/model_data_provider_interface.h>
#include <ts/texture_model.h>


// Model data provider implementation that reads from npy files as generated by the python version
class FTextureSynthesisModelDataProvider : public TITAN_NAMESPACE::ts::IModel_Data_Provider
{
public:

	virtual ~FTextureSynthesisModelDataProvider() {}

	FMetaHumanTextureSynthesizerModelData ModelData;

	static FString GetParamsFilename()
	{
		return FString(TEXT("ts_params.json"));
	}

	static bool IsFolderValidForLoading(const FString& InTextureSynthesisFolderPath)
	{
		// Only look for the params json file
		return	FPaths::DirectoryExists(InTextureSynthesisFolderPath) &&
				FPaths::FileExists(InTextureSynthesisFolderPath / GetParamsFilename());
	}

	static TITAN_NAMESPACE::ts::Model_Data ToTitanModelData(FMetaHumanTextureSynthesizerModelData::FModelDataContainer& InModelDataMap)
	{
		return TITAN_NAMESPACE::ts::Model_Data(
			InModelDataMap.NumRows, 
			InModelDataMap.NumColumns, 
			1, 
			InModelDataMap.WordSize, 
			InModelDataMap.DataBuffer.GetData());
	}

	virtual TITAN_NAMESPACE::ts::Model_Data load(TITAN_NAMESPACE::ts::TextureType type, TITAN_NAMESPACE::ts::Frequency frequency, int map_id, int HF_id) override
	{
		switch (type) {
		case TITAN_NAMESPACE::ts::TextureType::ALBEDO:
			{
				if (frequency == TITAN_NAMESPACE::ts::Frequency::HF)
				{
					check(!ModelData.AlbedoHFMaps[map_id].DataBuffer.IsEmpty());
					return ToTitanModelData(ModelData.AlbedoHFMaps[map_id]).row_view_as_image(HF_id, ModelData.HFResolution);
				}
				else
				{
					check(!ModelData.AlbedoLFMaps[map_id].DataBuffer.IsEmpty());
					return ToTitanModelData(ModelData.AlbedoLFMaps[map_id]).row_view_as_image(HF_id, ModelData.LFResolution);
				}
			}
		case TITAN_NAMESPACE::ts::TextureType::NORMAL:
			check(!ModelData.NormalHFMaps[map_id].DataBuffer.IsEmpty());
			check(frequency == TITAN_NAMESPACE::ts::Frequency::HF);
			return ToTitanModelData(ModelData.NormalHFMaps[map_id]).row_view_as_image(HF_id, ModelData.HFResolution);
		case TITAN_NAMESPACE::ts::TextureType::CAVITY:
			check(frequency == TITAN_NAMESPACE::ts::Frequency::HF);
			return ToTitanModelData(ModelData.CavityHFMap).row_view_as_image(HF_id, ModelData.HFResolution);
		default:
			break;
		}

		// return empty Model_Data
		return TITAN_NAMESPACE::ts::Model_Data{};
	}

	virtual TITAN_NAMESPACE::ts::Model_Data load(TITAN_NAMESPACE::ts::TextureType texture_type) override
	{
		return ToTitanModelData(ModelData.SynthesisModelData[TITAN_NAMESPACE::ts::Data_Type::LF_model]);
	}

	virtual TITAN_NAMESPACE::ts::Model_Data load(TITAN_NAMESPACE::ts::Data_Type type) override
	{
		return ToTitanModelData(ModelData.SynthesisModelData[type]);
	}
};


struct FMetaHumanFaceTextureSynthesizer::FImpl
{
	FTextureSynthesisModelDataProvider ModelDataProvider;
	TSharedPtr<TITAN_NAMESPACE::ts::TextureModel> TextureModel;
	TSharedPtr<TITAN_NAMESPACE::TaskThreadPool> TextureSynthesisThreadPool;
	int32 MaxHighFrequencyIndex;
	FMetaHumanFaceTextureAttributeMap FaceTextureAttributeMap;
};


FMetaHumanFaceTextureSynthesizer::FMetaHumanFaceTextureSynthesizer()
{
}

bool FMetaHumanFaceTextureSynthesizer::Init(const FString& InTextureSynthesisFolderPath, int32 InNumThreads)
{
	if (IsValid())
	{
		Clear();
	}
	Impl = MakePimpl<FImpl>();
	
	// Check if we can load the model data
	if (!FTextureSynthesisModelDataProvider::IsFolderValidForLoading(InTextureSynthesisFolderPath))
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Warning, "Texture synthesis data directory {TextureSynthesisFolderPath} does not contain valid data", InTextureSynthesisFolderPath);
		return false;
	}

	// Load the binary data used during synthesis
	Impl->ModelDataProvider.ModelData.Load(InTextureSynthesisFolderPath);

	// Load the texture model
	Impl->TextureModel = MakeShared<TITAN_NAMESPACE::ts::TextureModel>();
	if (!Impl->TextureModel->Load(TCHAR_TO_UTF8(*(InTextureSynthesisFolderPath / FTextureSynthesisModelDataProvider::GetParamsFilename())), Impl->ModelDataProvider))
	{
		return false;
	}

	// Clear temporary data to free some memory
	Impl->ModelDataProvider.ModelData.SynthesisModelData.Reset();

	// Store the max HF index for later translating float values to HF indices
	TITAN_NAMESPACE::ts::TextureModelParams TSModelParams = Impl->TextureModel->Parameters();
	Impl->MaxHighFrequencyIndex = TSModelParams.n_HF_index;

	// Setup the thread pool used for texture synthesis
	if (InNumThreads > 0)
	{
		Impl->TextureSynthesisThreadPool = MakeShared<TITAN_NAMESPACE::TaskThreadPool>(InNumThreads);
	}

	Impl->FaceTextureAttributeMap.Init(InTextureSynthesisFolderPath, Impl->MaxHighFrequencyIndex);

	return true;
}

bool FMetaHumanFaceTextureSynthesizer::IsValid() const
{
	return Impl.IsValid() && Impl->TextureModel && Impl->TextureModel->IsValid();
}

void FMetaHumanFaceTextureSynthesizer::Clear()
{
	// Safe to call even for null data
	Impl.Reset();
}

int32 FMetaHumanFaceTextureSynthesizer::GetMaxHighFrequencyIndex() const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return 0;
	}

	return Impl->MaxHighFrequencyIndex;
}

int32 FMetaHumanFaceTextureSynthesizer::GetTextureSizeX() const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return 0;
	}

	return Impl->ModelDataProvider.ModelData.HFResolution;
}

int32 FMetaHumanFaceTextureSynthesizer::GetTextureSizeY() const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return 0;
	}

	return Impl->ModelDataProvider.ModelData.HFResolution;
}

ERawImageFormat::Type FMetaHumanFaceTextureSynthesizer::GetTextureFormat() const
{
	// TODO: read from model params

	return ERawImageFormat::BGRA8;
}

EGammaSpace FMetaHumanFaceTextureSynthesizer::GetTextureColorSpace() const
{
	// TODO: read from model params

	return EGammaSpace::sRGB;
}

TArray<FMetaHumanFaceTextureSynthesizer::EMapType> FMetaHumanFaceTextureSynthesizer::GetSupportedAlbedoMapTypes() const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return {};
	}

	TArray<FMetaHumanFaceTextureSynthesizer::EMapType> OutMapTypes;
	for (int32 i = 0; i < 4; ++i)
	{
		if (!Impl->ModelDataProvider.ModelData.AlbedoHFMaps[i].DataBuffer.IsEmpty())
		{
			OutMapTypes.Add(static_cast<FMetaHumanFaceTextureSynthesizer::EMapType>(i));
		}

	}

	return OutMapTypes;
}

TArray<FMetaHumanFaceTextureSynthesizer::EMapType> FMetaHumanFaceTextureSynthesizer::GetSupportedNormalMapTypes() const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return {};
	}

	TArray<FMetaHumanFaceTextureSynthesizer::EMapType> OutMapTypes;
	for (int32 i = 0; i < 4; ++i)
	{
		if (!Impl->ModelDataProvider.ModelData.NormalHFMaps[i].DataBuffer.IsEmpty())
		{
			OutMapTypes.Add(static_cast<FMetaHumanFaceTextureSynthesizer::EMapType>(i));
		}

	}

	return OutMapTypes;
}

FLinearColor FMetaHumanFaceTextureSynthesizer::GetSkinTone(const FVector2f& InUV) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return {};
	}

	using namespace TITAN_NAMESPACE::ts;
	const TextureModel::Vector2 V = Impl->TextureModel->VuiToV(TextureModel::Vector2(InUV.X, InUV.Y));
	const TextureModel::Vector3 SkinTone = Impl->TextureModel->SkinTone(V);
	return FLinearColor(SkinTone[0], SkinTone[1], SkinTone[2]);
}

FVector2f FMetaHumanFaceTextureSynthesizer::ProjectSkinTone(const FLinearColor& InSkinTone) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return {};
	}

	using namespace TITAN_NAMESPACE::ts;
	const TextureModel::Vector2 VProj = Impl->TextureModel->ProjectSkinTone(TextureModel::Vector3{ InSkinTone.R, InSkinTone.G, InSkinTone.B });
	const TextureModel::Vector2 VuiProj = Impl->TextureModel->VToVui(VProj);
	const float U = FMath::Clamp(VuiProj(0), 0.0f, 1.0f);
	const float V = FMath::Clamp(VuiProj(1), 0.0f, 1.0f);
	return FVector2f{ U, V };
}

FVector3f FMetaHumanFaceTextureSynthesizer::GetBodyAlbedoGain(const FVector2f& InUV) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return {};
	}

	using namespace TITAN_NAMESPACE::ts;
	const TextureModel::Vector3 Gain = Impl->TextureModel->BodyAlbedoGain(TextureModel::Vector2(InUV.X, InUV.Y));
	return FVector3f(Gain[0], Gain[1], Gain[2]);
}

bool FMetaHumanFaceTextureSynthesizer::SynthesizeAlbedo(const FTextureSynthesisParams& InTextureSynthesisParams, FImageView OutAlbedoImage) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return false;
	}

	// Translate TS params to titan module types
	const TITAN_NAMESPACE::ts::TextureModel::Vector2 vui(InTextureSynthesisParams.SkinUVFromUI.X, InTextureSynthesisParams.SkinUVFromUI.Y);
	const int HF_chr_index = InTextureSynthesisParams.HighFrequencyIndex;
	const int map_id = static_cast<int>(InTextureSynthesisParams.MapType);
	check(HF_chr_index < Impl->MaxHighFrequencyIndex);
	check(map_id < 4);

	// Synthesize the albedo
	check(OutAlbedoImage.GetImageSizeBytes() == 4 * GetTextureSizeX() * GetTextureSizeY());
	Eigen::Vector4<uint8_t>* ImageRawData = reinterpret_cast<Eigen::Vector4<uint8_t>*>(OutAlbedoImage.RawData);

	const TITAN_NAMESPACE::ts::TextureModel::SynthesizeParams SynthesizeParams{
		.texture_type = TITAN_NAMESPACE::ts::TextureType::ALBEDO,
		.v_ui = vui,
		.HF_index = HF_chr_index,
		.map_id = map_id,
		.resolution = GetTextureSizeX()
	};
	if (!Impl->TextureModel->SynthesizeAlbedo(ImageRawData, SynthesizeParams, Impl->ModelDataProvider, Impl->TextureSynthesisThreadPool.IsValid() ? Impl->TextureSynthesisThreadPool.Get() : nullptr))
	{
		return false;
	} 

	return true;
}

bool FMetaHumanFaceTextureSynthesizer::SynthesizeAlbedoWithHF(const FTextureSynthesisParams& InTextureSynthesisParams, const TStaticArray<TArray<uint8>, 4>& InHFMaps, FImageView OutAlbedoImage) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return false;
	}

	if (InHFMaps[0].IsEmpty())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Cannot synthesize textures without neutral HF Map");
		return false;
	}

	// TS model data provider which overrides the HF map for a specific HD map
	// The rest of the load functions are forwarded to a reference data provider
	struct FOverrideHFDataProvider : public TITAN_NAMESPACE::ts::IModel_Data_Provider
	{
		FOverrideHFDataProvider(int32 InHFOverrideIndex, int32 InHFMapId, const TStaticArray<TArray<uint8>, 4>& InHFMaps, TITAN_NAMESPACE::ts::IModel_Data_Provider& InRefDataProvider) :
			HFResolution(static_cast<int32>(FMath::Sqrt(InHFMaps[0].Num() / 3.0f))),
			HFOverrideIndex(InHFOverrideIndex),
			HFMapId(InHFMapId),
			HFMaps(InHFMaps),
			RefDataProvider(InRefDataProvider)
		{
		}

		const int32 HFResolution;
		const int32 HFOverrideIndex;
		const int32 HFMapId;
		const TStaticArray<TArray<uint8>, 4>& HFMaps;
		TITAN_NAMESPACE::ts::IModel_Data_Provider& RefDataProvider;

		virtual TITAN_NAMESPACE::ts::Model_Data load(TITAN_NAMESPACE::ts::TextureType type, TITAN_NAMESPACE::ts::Frequency frequency, int map_id, int HF_id) override
		{
			if (frequency == TITAN_NAMESPACE::ts::Frequency::HF && HF_id == HFOverrideIndex /*&& map_id == HFMapId*/)
			{
				return TITAN_NAMESPACE::ts::Model_Data(HFResolution, HFResolution, 3, 1, const_cast<uint8*>(HFMaps[map_id].GetData()));
			}

			return RefDataProvider.load(type, frequency, map_id, HF_id);
		}

		virtual TITAN_NAMESPACE::ts::Model_Data load(TITAN_NAMESPACE::ts::TextureType texture_type) override
		{
			return RefDataProvider.load(texture_type);
		}

		virtual TITAN_NAMESPACE::ts::Model_Data load(TITAN_NAMESPACE::ts::Data_Type type) override
		{
			return RefDataProvider.load(type);
		}
	};

	// Translate TS params to titan module types
	const TITAN_NAMESPACE::ts::TextureModel::Vector2 vui(InTextureSynthesisParams.SkinUVFromUI.X, InTextureSynthesisParams.SkinUVFromUI.Y);
	const int HF_chr_index = InTextureSynthesisParams.HighFrequencyIndex;
	const int map_id = static_cast<int>(InTextureSynthesisParams.MapType);
	check(HF_chr_index < Impl->MaxHighFrequencyIndex);
	check(map_id < 4);

	FOverrideHFDataProvider OverrideModelData(InTextureSynthesisParams.HighFrequencyIndex, map_id, InHFMaps, Impl->ModelDataProvider);

	// Synthesize the albedo
	const TITAN_NAMESPACE::ts::TextureModel::SynthesizeParams SynthesizeParams{
		.texture_type = TITAN_NAMESPACE::ts::TextureType::ALBEDO,
		.v_ui = vui,
		.HF_index = HF_chr_index,
		.map_id = map_id,
		.animated_delta = map_id > 0,	// Treat animated maps as delta, TODO: make this a setting in the MetaHumanFaceTextureSynthesizer
		.resolution = OverrideModelData.HFResolution
	};

	check(OutAlbedoImage.GetImageSizeBytes() == 4 * SynthesizeParams.resolution * SynthesizeParams.resolution);
	Eigen::Vector4<uint8_t>* ImageRawData = reinterpret_cast<Eigen::Vector4<uint8_t>*>(OutAlbedoImage.RawData);
	if (!Impl->TextureModel->GetTexture(ImageRawData, SynthesizeParams, OverrideModelData, Impl->TextureSynthesisThreadPool.IsValid() ? Impl->TextureSynthesisThreadPool.Get() : nullptr))
	{
		return false;
	}

	return true;
}

bool FMetaHumanFaceTextureSynthesizer::SelectNormal(const FTextureSynthesisParams& InTextureSynthesisParams, FImageView OutNormalImage) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return false;
	}

	// Translate TS params to titan module types
	const TITAN_NAMESPACE::ts::TextureModel::Vector2 vui(InTextureSynthesisParams.SkinUVFromUI.X, InTextureSynthesisParams.SkinUVFromUI.Y);
	const int HF_chr_index = InTextureSynthesisParams.HighFrequencyIndex;
	const int map_id = static_cast<int>(InTextureSynthesisParams.MapType);
	check(HF_chr_index < Impl->MaxHighFrequencyIndex);
	check(map_id < 4);

	check(OutNormalImage.GetImageSizeBytes() == 4 * GetTextureSizeX() * GetTextureSizeY());
	Eigen::Vector4<uint8_t>* ImageRawData = reinterpret_cast<Eigen::Vector4<uint8_t>*>(OutNormalImage.RawData);

	const TITAN_NAMESPACE::ts::TextureModel::SynthesizeParams SynthesizeParams{
		.texture_type = TITAN_NAMESPACE::ts::TextureType::NORMAL,
		//.v_ui = vui,	// skin tone is not used for selecting the normal
		.HF_index = HF_chr_index,
		.map_id = map_id,
		.resolution = GetTextureSizeX()
	};
	if (!Impl->TextureModel->DirectSelection(ImageRawData, SynthesizeParams, Impl->ModelDataProvider, Impl->TextureSynthesisThreadPool.IsValid() ? Impl->TextureSynthesisThreadPool.Get() : nullptr))
	{
		return false;
	}

	return true;
}

bool FMetaHumanFaceTextureSynthesizer::SelectCavity(int32 HighFrequencyIndex, FImageView OutCavityImage) const
{
	if (!IsValid())
	{
		UE_LOGFMT(LogMetaHumanCoreTechLib, Error, "Texture Synthesis has not been initialized");
		return false;
	}

	check(OutCavityImage.GetImageSizeBytes() == 4 * GetTextureSizeX() * GetTextureSizeY());
	Eigen::Vector4<uint8_t>* ImageRawData = reinterpret_cast<Eigen::Vector4<uint8_t>*>(OutCavityImage.RawData);

	const TITAN_NAMESPACE::ts::TextureModel::SynthesizeParams SynthesizeParams{
		.texture_type = TITAN_NAMESPACE::ts::TextureType::CAVITY,
		.HF_index = HighFrequencyIndex,
		.map_id = 0,
		.resolution = GetTextureSizeX()
	};
	if (!Impl->TextureModel->DirectSelection(ImageRawData, SynthesizeParams, Impl->ModelDataProvider, Impl->TextureSynthesisThreadPool.IsValid() ? Impl->TextureSynthesisThreadPool.Get() : nullptr))
	{
		return false;
	}

	return true;
}

bool FMetaHumanFaceTextureAttributeMap::Init(const FString& InTextureSynthesisFolderPath, int32 NumTextures)
{
	AttributeNames.Reset();
	AttributeValueNames.Reset();
	AttributeValues.Reset();
	AllIndices.Reset();
	for (int32 Idx = 0; Idx < NumTextures; ++Idx)
	{
		AllIndices.Push(Idx);
	}
	// Load the texture indices per roughness
	FString JsonString;
	FString JsonFilePath = InTextureSynthesisFolderPath / FString("texture_attributes.json");
	if (FFileHelper::LoadFileToString(JsonString, *JsonFilePath))
	{
		TSharedPtr<FJsonObject> JsonObject;
		TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(JsonString);
		if (FJsonSerializer::Deserialize(JsonReader, JsonObject))
		{
			const TArray<TSharedPtr<FJsonValue>>* AttributeOrderJsonArray;
			if (!JsonObject->TryGetArrayField(TEXT("order"), AttributeOrderJsonArray))
			{
				UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Attribute order is missing."));
				return false;
			}
			const TSharedPtr<FJsonObject>* AttributesJson;
			if (!JsonObject->TryGetObjectField(TEXT("attributes"), AttributesJson))
			{
				UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Attributes are missing from json file."));
				return false;
			}
			for (int32 Idx = 0; Idx < AttributeOrderJsonArray->Num(); ++Idx)
			{
				FString AttributeName;
				if (!(*AttributeOrderJsonArray)[Idx]->TryGetString(AttributeName))
				{
					UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Expected attribute names."));
					return false;
				}
				AttributeNames.Push(AttributeName);
				AttributeValueNames.Push(TArray<FString>());
				AttributeValues.Push(TArray<int32>());

				const TSharedPtr<FJsonObject>* AttributeJson;
				if (!(*AttributesJson)->TryGetObjectField(AttributeName, AttributeJson))
				{
					UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Attribute order is missing."));
					return false;
				}
				const TArray<TSharedPtr<FJsonValue>>* AttributeValueNamesJsonArray;
				if (!(*AttributeJson)->TryGetArrayField(TEXT("names"), AttributeValueNamesJsonArray))
				{
					UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Expected attribute value names."));
					return false;
				}
				for (int32 NameIdx = 0; NameIdx < AttributeValueNamesJsonArray->Num(); ++NameIdx)
				{
					FString AttributeValueName;
					if (!(*AttributeValueNamesJsonArray)[NameIdx]->TryGetString(AttributeValueName))
					{
						UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Expected attribute value name."));
						return false;
					}
					AttributeValueNames.Last().Push(AttributeValueName);
				}
				const TArray<TSharedPtr<FJsonValue>>* AttributeValuesJsonArray;
				if (!(*AttributeJson)->TryGetArrayField(TEXT("values"), AttributeValuesJsonArray))
				{
					UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Expected attribute values."));
					return false;
				}
				if (AttributeValuesJsonArray->Num() != NumTextures)
				{
					UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Invalid number of attribute values: %d, expected %d."), AttributeValuesJsonArray->Num(), NumTextures);
					return false;
				}
				for (int32 ValueIdx = 0; ValueIdx < AttributeValuesJsonArray->Num(); ++ValueIdx)
				{
					int32 Value;
					if (!(*AttributeValuesJsonArray)[ValueIdx]->TryGetNumber(Value))
					{
						UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Expected attribute value."));
						return false;
					}
					if (Value < 0 || Value >= AttributeValueNames.Last().Num())
					{
						UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Invalid attribute value: %d not in range [0, %d) for %s"), Value, AttributeValueNames.Last().Num(), *AttributeNames.Last());
						return false;
					}
					AttributeValues.Last().Push(Value);
				}
			}
		}
		else
		{
			UE_LOG(LogMetaHumanCoreTechLib, Error, TEXT("Cannot parse json file %s"), *JsonFilePath);
			return false;
		}
	}
	return true;
}

const FString& FMetaHumanFaceTextureAttributeMap::GetAttributeName(int32 Idx) const
{
	check(Idx >= 0 && Idx < AttributeValues.Num());
	return AttributeNames[Idx];
}

const TArray<FString>& FMetaHumanFaceTextureAttributeMap::GetAttributeValueNames(int32 Idx) const
{
	check(Idx >= 0 && Idx < AttributeValues.Num());
	return AttributeValueNames[Idx];
}

const TArray<int32>& FMetaHumanFaceTextureAttributeMap::GetAttributeValues(int32 Idx) const
{
	check(Idx >= 0 && Idx < AttributeValues.Num());
	return AttributeValues[Idx];
}

TArray<int32> FMetaHumanFaceTextureAttributeMap::Filter(int32 AttributeIndex, int32 AttributeValue, const TArray<int32>& InIndices) const
{
	check(AttributeIndex >= 0 && AttributeIndex < AttributeNames.Num());
	check(AttributeValue >= 0 && AttributeValue < AttributeValueNames[AttributeIndex].Num());
	TArray<int32> Indices;
	for (int32 Idx = 0; Idx < InIndices.Num(); ++Idx)
	{
		if (AttributeValues[AttributeIndex][InIndices[Idx]] == AttributeValue)
		{
			Indices.Push(InIndices[Idx]);
		}
	}
	return Indices;
}

const FMetaHumanFaceTextureAttributeMap& FMetaHumanFaceTextureSynthesizer::GetFaceTextureAttributeMap() const
{
	return Impl->FaceTextureAttributeMap;
}

FMetaHumanFilteredFaceTextureIndices::FMetaHumanFilteredFaceTextureIndices(const FMetaHumanFaceTextureAttributeMap& FaceTextureAttributeMap, const TArray<int32>& AttributeValues)
{
	check(AttributeValues.Num() == FaceTextureAttributeMap.NumAttributes());

	Indices = FaceTextureAttributeMap.GetAllIndices();
	for (int32 AttributeIdx = 0; AttributeIdx < AttributeValues.Num(); ++AttributeIdx)
	{
		int32 AttributeValue = AttributeValues[AttributeIdx];
		if (AttributeValue >= 0)
		{
			Indices = FaceTextureAttributeMap.Filter(AttributeIdx, AttributeValue, Indices);
		}
	}
}

int32 FMetaHumanFilteredFaceTextureIndices::ConvertTextureIndexToFilterIndex(int32 TextureIndex) const
{
	for (int32 FilterIndex = 0; FilterIndex < Num(); ++FilterIndex)
	{
		if (Indices[FilterIndex] == TextureIndex)
		{
			return FilterIndex;
		}
	}
	return -1;
}

int32 FMetaHumanFilteredFaceTextureIndices::ConvertFilterIndexToTextureIndex(int32 FilterIndex) const
{
	if (FilterIndex >= 0 && FilterIndex < Num())
	{
		return Indices[FilterIndex];
	}
	return -1;
}