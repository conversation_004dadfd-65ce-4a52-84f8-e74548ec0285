// Copyright Epic Games, Inc. All Rights Reserved.

namespace UnrealBuildTool.Rules
{
	public class PCGCompute : ModuleRules
	{
		public PCGCompute(ReadOnlyTargetRules Target) : base(Target)
		{
			PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

			PublicDependencyModuleNames.AddRange(
				new string[]
				{
					"Core",
					"Projects",
					"RHI",
					"RenderCore",
					"Renderer",
				});

			PrivateDependencyModuleNames.AddRange(
				new string[]
				{
					"Engine",
				});
		}
	}
}
