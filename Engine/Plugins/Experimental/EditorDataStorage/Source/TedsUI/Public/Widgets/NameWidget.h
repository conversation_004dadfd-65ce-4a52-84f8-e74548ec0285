// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Elements/Columns/TypedElementMiscColumns.h"
#include "Elements/Interfaces/TypedElementDataStorageFactory.h"
#include "Elements/Interfaces/TypedElementDataStorageUiInterface.h"
#include "Internationalization/Text.h"
#include "UObject/ObjectMacros.h"

#include "NameWidget.generated.h"

class IEditorDataStorageProvider;
class UScriptStruct;

namespace UE::Editor::DataStorage
{
	class FNameWidgetSorter_NoSlash final : public TColumnSorterInterface<FColumnSorterInterface::ESortType::HybridSort, FNameColumn>
	{
	public:
		virtual FText GetShortName() const override;

	protected:
		virtual int32 Compare(const FNameColumn& Left, const FNameColumn& Right) const override;
		virtual FPrefixInfo CalculatePrefix(const FNameColumn& Column, uint32 ByteIndex) const override;
	};

	class FNameWidgetSorter_WithSlash final : public TColumnSorterInterface<FColumnSorterInterface::ESortType::HybridSort, FNameColumn>
	{
	public:
		virtual FText GetShortName() const override;

	protected:
		virtual int32 Compare(const FNameColumn& Left, const FNameColumn& Right) const override;
		virtual FPrefixInfo CalculatePrefix(const FNameColumn& Column, uint32 ByteIndex) const override;
	};
}

UCLASS()
class UNameWidgetFactory : public UEditorDataStorageFactory
{
	GENERATED_BODY()

public:
	virtual ~UNameWidgetFactory() override = default;

	TEDSUI_API virtual void RegisterWidgetConstructors(UE::Editor::DataStorage::ICoreProvider& DataStorage,
		UE::Editor::DataStorage::IUiProvider& DataStorageUi) const override;
};

// Label for assets in TEDS
USTRUCT()
struct FNameWidgetConstructor : public FSimpleWidgetConstructor
{
	GENERATED_BODY()

public:
	TEDSUI_API FNameWidgetConstructor();

	TEDSUI_API virtual TSharedPtr<SWidget> CreateWidget(
		UE::Editor::DataStorage::ICoreProvider* DataStorage,
		UE::Editor::DataStorage::IUiProvider* DataStorageUi,
		UE::Editor::DataStorage::RowHandle TargetRow,
		UE::Editor::DataStorage::RowHandle WidgetRow, 
		const UE::Editor::DataStorage::FMetaDataView& Arguments) override;

	virtual TArray<TSharedPtr<const UE::Editor::DataStorage::FColumnSorterInterface>> ConstructColumnSorters(
		UE::Editor::DataStorage::ICoreProvider* DataStorage,
		const UE::Editor::DataStorage::FMetaDataView& Arguments) override;
};
