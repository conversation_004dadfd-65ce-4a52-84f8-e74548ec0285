<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>utf8-range</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: utf8-range
    Download Link: https://vcpkg.io/en/package/utf8-range.html
    Version: 5.29.3
    Notes: It is part of IREE. The compiled shared libraries are used by an executable to convert neural networks. It is distributed with the engine but not in games.
        -->
<Location>Engine/Plugins/Experimental/NNERuntimeIREE/Source/ThirdParty/IREE/</Location>
<Function>It is part of IREE. The shared libraries are used by a binary to convert neural network models inside the Editor.</Function>
<Eula>https://github.com/protocolbuffers/protobuf/blob/main/third_party/utf8_range/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensee</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>