// Copyright Epic Games, Inc. All Rights Reserved.

#include "../NiagaraStatelessCommon.ush"

static const uint EInitializeParticleModuleFlag_UniformSpriteSize	= 1 << 0;
static const uint EInitializeParticleModuleFlag_UniformMeshScale	= 1 << 1;

uint	InitializeParticle_ModuleFlags;
FNiagaraStatelessBuiltDistributionType	InitializeParticle_InitialPosition;
float4	InitializeParticle_ColorScale;
float4	InitializeParticle_ColorBias;
float2	InitializeParticle_SpriteSizeScale;
float2	InitializeParticle_SpriteSizeBias;
float	InitializeParticle_SpriteRotationScale;
float	InitializeParticle_SpriteRotationBias;

float3	InitializeParticle_MeshScaleScale;
float3	InitializeParticle_MeshScaleBias;

float	InitializeParticle_RibbonWidthScale;
float	InitializeParticle_RibbonWidthBias;
	
void InitializeParticle_Simulate(inout FStatelessParticle Particle)
{  
	const bool bUniformSpriteSize = (InitializeParticle_ModuleFlags & EInitializeParticleModuleFlag_UniformSpriteSize) != 0;
	const bool bUniformMeshScale  = (InitializeParticle_ModuleFlags & EInitializeParticleModuleFlag_UniformMeshScale) != 0;

	FStatelessDistributionSamplerFloat3 InitialPosition;
	InitialPosition.Init(0, InitializeParticle_InitialPosition);

	Particle.Position		= InitialPosition.GetValue(0.0f);
	Particle.Color			= RandomScaleBiasFloat(1, InitializeParticle_ColorScale, InitializeParticle_ColorBias);
	Particle.RibbonWidth	= RandomScaleBiasFloat(2, InitializeParticle_RibbonWidthScale, InitializeParticle_RibbonWidthBias);
	Particle.SpriteSize		= RandomScaleBiasFloat(3, InitializeParticle_SpriteSizeScale, InitializeParticle_SpriteSizeBias, bUniformSpriteSize);
	Particle.SpriteRotation	= RandomScaleBiasFloat(4, InitializeParticle_SpriteRotationScale, InitializeParticle_SpriteRotationBias);
	Particle.Scale			= RandomScaleBiasFloat(5, InitializeParticle_MeshScaleScale, InitializeParticle_MeshScaleBias, bUniformMeshScale);

	Particle.PreviousPosition		= Particle.Position;
	Particle.PreviousRibbonWidth	= Particle.RibbonWidth;
	Particle.PreviousSpriteSize		= Particle.SpriteSize;
	Particle.PreviousScale			= Particle.Scale;
	Particle.PreviousSpriteRotation	= Particle.SpriteRotation;
}
