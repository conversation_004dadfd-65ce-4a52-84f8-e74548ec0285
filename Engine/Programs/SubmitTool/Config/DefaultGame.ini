[Staging]
+RemapDirectories=(From="SubmitTool/Script", To="Engine/Binaries")
+AllowedDirectories=Engine/Content/Editor/Slate
+AllowedDirectories=Engine/Content/Slate
+AllowedConfigFiles=Engine/Config/BaseEditorPerProjectUserSettings.ini
+AllowedConfigFiles=SubmitTool/Config/SubmitTool.ini
+AllowedConfigFiles=SubmitTool/Config/DeployId.ini
+AllowedConfigFiles=SubmitTool/Config/Mac/MacSubmitTool.ini
+AllowedConfigFiles=SubmitTool/Config/Linux/LinuxSubmitTool.ini
+AllowedConfigFiles=SubmitTool/Config/Windows/WindowsSubmitTool.ini

[/Script/UnrealEd.ProjectPackagingSettings]
+DirectoriesToAlwaysStageAsNonUFS=(Path="../Script")