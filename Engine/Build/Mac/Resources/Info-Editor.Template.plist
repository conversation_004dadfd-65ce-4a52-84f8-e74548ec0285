<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSMicrophoneUsageDescription</key>
	<string>UnrealEditor requires microphone access for media capture</string>
	<key>NSCameraUsageDescription</key>
	<string>UnrealEditor requires camera access for media capture</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>UnrealEditor does not utilize this functionality but it is included in our Info.plist since our app utilizes a library which references this permission in its code.</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>uproject</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>UProject</string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>Unreal Project</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.epicgames.uproject</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
		</dict>
	</array>
	<key>CFBundleIconFile</key>
	<string>AppIcon</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSHighResolutionCapable</key>
	<true/>
	<key>NSHighResolutionMagnifyAllowed</key>
	<false/>
	<key>CFBundleIdentifier</key>
	<string>com.epicgames.UnrealEditor</string>
</dict>
</plist>
