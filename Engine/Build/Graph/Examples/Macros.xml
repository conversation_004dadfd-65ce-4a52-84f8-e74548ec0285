<?xml version='1.0' ?>
<BuildGraph xmlns="http://www.epicgames.com/BuildGraph" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../Schema.xsd" >


	<!-- Define a macro that will print out three log lines -->
	<Macro Name="MyTestMacro" Arguments="PrintFirstMessage;PrintSecondMessage" OptionalArguments="PrintThirdMessage">
		<Log Message="First message" If="$(PrintFirstMessage)"/>
		<Log Message="Second message" If="$(PrintSecondMessage)"/>
		
		<!-- Optional Arguments will be empty if not specified, so need to guard against that -->
		<Log Message="Third message" If="'$(PrintThirdMessage)' == 'true'"/>
	</Macro>

	<!-- Define a macro that will print out properties in the parent scope. Note that these values are expanded in the lexical scope that the macro is expanded. -->
	<Macro Name="PrintThreeValues">
		<Log Message="First value: $(FirstValue)"/>
		<Log Message="Second value: $(SecondValue)"/>
		<Log Message="Third value: $(ThirdValue)"/>
	</Macro>
	
	<!-- Add a node to expand those macros -->
	<Agent Name="Test Agent" Type="CompileWin64">
		<Node Name="Test Macro Expansion">
			<Expand Name="MyTestMacro" PrintFirstMessage="true" PrintSecondMessage="true"/>
			<Expand Name="MyTestMacro" PrintFirstMessage="false" PrintSecondMessage="false" PrintThirdMessage="true"/>

			<Property Name="FirstValue" Value="123"/>
			<Property Name="SecondValue" Value="456"/>
			<Property Name="ThirdValue" Value="789"/>
			<Expand Name="PrintThreeValues"/>
		</Node>
	</Agent>
	
</BuildGraph>
