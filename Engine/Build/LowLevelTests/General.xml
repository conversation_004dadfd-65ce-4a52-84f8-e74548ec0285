<?xml version="1.0" encoding="utf-8"?>
<BuildGraph xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../Build/Graph/Schema.xsd" xmlns="http://www.epicgames.com/BuildGraph">
	<Extend Name="Catch2BuildAppendExtraCMakeArgsPlatform">
		<Do If="'$(Platform)' == 'Win64'">
			<Expand Name="Catch2BuildAppendExtraCMakeArgs"
					ExtraArgValue="-DCOMPILER_EXCEPTION_FLAG:STRING=&quot;/EHsc&quot; -DSET_CONFIG_NO_COLOUR_WIN32=OFF"/>
		</Do>
		<Do If="'$(Platform)' == 'Mac' OR '$(Platform)' == 'Linux' OR '$(Platform)' == 'LinuxArm64'">
			<Expand Name="Catch2BuildAppendExtraCMakeArgs"
					ExtraArgValue="-DCOMPILER_EXCEPTION_FLAG:STRING=&quot;-fexceptions&quot;"/>
		</Do>
		<Do If="'$(Platform)' == 'Linux' OR '$(Platform)' == 'LinuxArm64'">
			<Expand Name="Catch2BuildAppendExtraCMakeArgs"
					ExtraArgValue="-DBUILD_WITH_LIBCXX=ON"/>
		</Do>
	</Extend>
	<Macro Name="LinuxArm64DockerSetup" Arguments="AppNameLowercase;AppDir">
		<!-- Build a linux docker image with arm64 emulator utilities -->
		<Property Name="AppDirFiles" Value="$(AppDir)/..."/>
		<Tag Files="$(AppDirFiles)" With="#DockerBuildFiles"/>
		<Copy From="$(RootDir)/Engine/Build/LowLevelTests/LinuxArm64/" To="$(AppDir)/" Files="dockerfile"/>
		<Docker-Build BaseDir="$(AppDir)" DockerFile="$(AppDir)/dockerfile" Files="..." Tag="$(AppNameLowercase)-linuxarm64-image" Arguments="--build-arg app_base_dir=$(AppDir)" UseBuildKit="False"/>
	</Macro>
	<Macro Name="LinuxArm64DockerCleanup" Arguments="AppNameLowercase">
		<Do If="!$(IsBuildMachine)">
			<Spawn Exe="$(RootDir)/Engine/Build/LowLevelTests/LinuxArm64/docker_cleanup.bat" Arguments="$(AppNameLowercase)"/>
		</Do>
		<Do If="$(IsBuildMachine)">
			<Spawn Exe="sh" Arguments="$(RootDir)/Engine/Build/LowLevelTests/LinuxArm64/docker_cleanup.sh $(AppNameLowercase)"/>
		</Do>
	</Macro>
</BuildGraph>