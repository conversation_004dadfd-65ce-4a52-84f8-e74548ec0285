<?xml version="1.0" encoding="utf-8"?>
<BuildGraph xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../Build/Graph/Schema.xsd" xmlns="http://www.epicgames.com/BuildGraph">
  <Option Name="RunStateGraphTests" DefaultValue="" Description="Run State Graph Tests" />
  <Property Name="TestNames" Value="$(TestNames);StateGraph" />
  <Extend Name="RunAllTests">
    <Expand Name="DeployAndTest" Platform="Win64" TestName="StateGraph" ShortName="State Graph" TargetName="StateGraphTests" BinaryRelativePath="Engine\Binaries" ReportType="console" />
  </Extend>
</BuildGraph>