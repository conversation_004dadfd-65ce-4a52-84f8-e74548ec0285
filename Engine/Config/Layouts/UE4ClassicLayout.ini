[EditorLayouts]
LayoutName=NSLOCTEXT("LayoutNamespace", "UE4_Classic_Layout", "UE4 Classic Layout")
LayoutDescription=NSLOCTEXT("LayoutNamespace", "The_default_UE4_layout", "The default UE4 layout")
UnrealEd_Layout_v1.5="(\\\r\n\t\"Type\": \"Layout\",\\\r\n\t\"Name\": \"UnrealEd_Layout_v1.5\",\\\r\n\t\"PrimaryAreaIndex\": 0,\\\r\n\t\"Areas\": [\\\r\n\t\t(\\\r\n\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\"Type\": \"Area\",\\\r\n\t\t\t\"Orientation\": \"Orient_Horizontal\",\\\r\n\t\t\t\"WindowPlacement\": \"Placement_NoWindow\",\\\r\n\t\t\t\"Nodes\": [\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 2,\\\r\n\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\"ForegroundTab\": \"LevelEditor\",\\\r\n\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"LevelEditor\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"DockedToolkit\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t)\\\r\n\t\t\t]\\\r\n\t\t),\\\r\n\t\t(\\\r\n\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\"Type\": \"Area\",\\\r\n\t\t\t\"Orientation\": \"Orient_Horizontal\",\\\r\n\t\t\t\"WindowPlacement\": \"Placement_Automatic\",\\\r\n\t\t\t\"WindowSize_X\": 2236,\\\r\n\t\t\t\"WindowSize_Y\": 909.99993896484375,\\\r\n\t\t\t\"Nodes\": [\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\"ForegroundTab\": \"None\",\\\r\n\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"ContentBrowser1Tab\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t)\\\r\n\t\t\t]\\\r\n\t\t),\\\r\n\t\t(\\\r\n\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\"Type\": \"Area\",\\\r\n\t\t\t\"Orientation\": \"Orient_Vertical\",\\\r\n\t\t\t\"WindowPlacement\": \"Placement_Automatic\",\\\r\n\t\t\t\"WindowSize_X\": 2236,\\\r\n\t\t\t\"WindowSize_Y\": 909.99993896484375,\\\r\n\t\t\t\"Nodes\": [\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\"ForegroundTab\": \"None\",\\\r\n\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"StandaloneToolkit\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t),\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 0.34999999403953552,\\\r\n\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\"ForegroundTab\": \"None\",\\\r\n\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"MergeTool\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t)\\\r\n\t\t\t]\\\r\n\t\t),\\\r\n\t\t(\\\r\n\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\"Type\": \"Area\",\\\r\n\t\t\t\"Orientation\": \"Orient_Horizontal\",\\\r\n\t\t\t\"WindowPlacement\": \"Placement_Automatic\",\\\r\n\t\t\t\"WindowSize_X\": 2236,\\\r\n\t\t\t\"WindowSize_Y\": 909.99993896484375,\\\r\n\t\t\t\"Nodes\": [\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\"ForegroundTab\": \"None\",\\\r\n\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"EditorSettings\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"ProjectSettings\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"PluginsEditor\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t)\\\r\n\t\t\t]\\\r\n\t\t)\\\r\n\t]\\\r\n)"
LevelEditor_Layout_v1.8="(\\\r\n\t\"Type\": \"Layout\",\\\r\n\t\"Name\": \"LevelEditor_Layout_v1.8\",\\\r\n\t\"PrimaryAreaIndex\": 0,\\\r\n\t\"Areas\": [\\\r\n\t\t(\\\r\n\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\"Type\": \"Area\",\\\r\n\t\t\t\"Orientation\": \"Orient_Horizontal\",\\\r\n\t\t\t\"WindowPlacement\": \"Placement_NoWindow\",\\\r\n\t\t\t\"Nodes\": [\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\"Type\": \"Splitter\",\\\r\n\t\t\t\t\t\"Orientation\": \"Orient_Vertical\",\\\r\n\t\t\t\t\t\"Nodes\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"SizeCoefficient\": 0.75,\\\r\n\t\t\t\t\t\t\t\"Type\": \"Splitter\",\\\r\n\t\t\t\t\t\t\t\"Orientation\": \"Orient_Horizontal\",\\\r\n\t\t\t\t\t\t\t\"Nodes\": [\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"SizeCoefficient\": 0.30000001192092896,\\\r\n\t\t\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\t\t\t\t\"ForegroundTab\": \"PlacementBrowser\",\\\r\n\t\t\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"TabId\": \"PlacementBrowser\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"TabId\": \"LevelEditorToolBox\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"SizeCoefficient\": 1.1499999761581421,\\\r\n\t\t\t\t\t\t\t\t\t\"Type\": \"Splitter\",\\\r\n\t\t\t\t\t\t\t\t\t\"Orientation\": \"Orient_Vertical\",\\\r\n\t\t\t\t\t\t\t\t\t\"Nodes\": [\\\r\n\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"HideTabWell\": true,\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"ForegroundTab\": \"LevelEditorToolBar\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"TabId\": \"LevelEditorToolBar\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"HideTabWell\": true,\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"ForegroundTab\": \"None\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"TabId\": \"EditorModeToolbar\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"HideTabWell\": true,\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"ForegroundTab\": \"LevelEditorViewport\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"TabId\": \"LevelEditorViewport\",\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"SizeCoefficient\": 0.40000000596046448,\\\r\n\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\t\t\"ForegroundTab\": \"ContentBrowserTab1\",\\\r\n\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"EmbeddedSequenceID\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"ContentBrowserTab1\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"OutputLog\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t),\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 0.30000001192092896,\\\r\n\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\"ForegroundTab\": \"None\",\\\r\n\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"TabId\": \"TakeRecorder\",\\\r\n\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t),\\\r\n\t\t\t\t(\\\r\n\t\t\t\t\t\"SizeCoefficient\": 0.25,\\\r\n\t\t\t\t\t\"Type\": \"Splitter\",\\\r\n\t\t\t\t\t\"Orientation\": \"Orient_Vertical\",\\\r\n\t\t\t\t\t\"Nodes\": [\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"SizeCoefficient\": 0.40000000596046448,\\\r\n\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\t\t\"ForegroundTab\": \"LevelEditorSceneOutliner\",\\\r\n\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"LevelEditorSceneOutliner\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"LevelEditorLayerBrowser\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\"SizeCoefficient\": 1,\\\r\n\t\t\t\t\t\t\t\"Type\": \"Stack\",\\\r\n\t\t\t\t\t\t\t\"HideTabWell\": false,\\\r\n\t\t\t\t\t\t\t\"ForegroundTab\": \"LevelEditorSelectionDetails\",\\\r\n\t\t\t\t\t\t\t\"Tabs\": [\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"LevelEditorSelectionDetails\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"OpenedTab\"\\\r\n\t\t\t\t\t\t\t\t),\\\r\n\t\t\t\t\t\t\t\t(\\\r\n\t\t\t\t\t\t\t\t\t\"TabId\": \"WorldSettingsTab\",\\\r\n\t\t\t\t\t\t\t\t\t\"TabState\": \"ClosedTab\"\\\r\n\t\t\t\t\t\t\t\t)\\\r\n\t\t\t\t\t\t\t]\\\r\n\t\t\t\t\t\t)\\\r\n\t\t\t\t\t]\\\r\n\t\t\t\t)\\\r\n\t\t\t]\\\r\n\t\t)\\\r\n\t]\\\r\n)"


