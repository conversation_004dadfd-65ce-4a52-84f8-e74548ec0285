<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Perforce .NET API 2013</Name>
  <Location>/Engine/Source/ThirdParty/Perforce/P4API.NET-Beta/</Location>
  <Date>2016-06-13T17:33:32.3674692-04:00</Date>
  <Function>Managed code interface for the editor and tools talking to Perforce servers</Function>
  <Justification>Managed code interface for the editor and tools talking to Perforce servers</Justification>
  <Eula>https://swarm.workshop.perforce.com/projects/perforce-software-p4api-net/files/main/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/Perforce.NETAPI2013_License.txt</LicenseFolder>
</TpsData>