<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>NVAPI</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: NVAPI
    Download Link: https://github.com/NVIDIA/nvapi
    Version: R570
    Notes: Using DirectX and HLSL extensions
        -->
<Location>//Fortnite/Main/Engine/Source/ThirdParty/NVIDIA/nvapi</Location>
<Function>NVAPI is NVIDIA's core software development kit that allows direct access to NVIDIA GPUs and drivers on all Windows platforms. NVAPI provides support for operations such as querying the installed driver version, enumerating GPUs and displays, monitoring GPU memory consumption, clocks, and temperature, DirectX and HLSL extensions, and more.</Function>
<Eula>https://github.com/NVIDIA/nvapi/blob/main/License.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensee</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>