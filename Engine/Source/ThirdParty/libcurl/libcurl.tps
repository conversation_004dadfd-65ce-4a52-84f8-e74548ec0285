<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>libcurl v7.47.1, v7.48.0, v7.55.1, 7,75,0</Name>
  <Location>/Engine/Source/ThirdParty/libcurl/</Location>
  <Function>HTTP client library used as a source code that is compiled into binary and integrated into UE/EOSSDK</Function>
  <Eula>http://curl.haxx.se/docs/copyright.html</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/Libcurl_License.txt</LicenseFolder>
</TpsData>