#!/bin/bash

set -e

LIBRARY_NAME="pugixml"
REPOSITORY_NAME="pugixml"

# Informational, for the usage message.
CURRENT_LIBRARY_VERSION=1.15

BUILD_SCRIPT_NAME="$(basename $BASH_SOURCE)"
BUILD_SCRIPT_DIR=`cd $(dirname "$BASH_SOURCE"); pwd`

UsageAndExit()
{
    echo "Build $LIBRARY_NAME for use with Unreal Engine on Linux"
    echo
    echo "Usage:"
    echo
    echo "    $BUILD_SCRIPT_NAME <$LIBRARY_NAME Version> <Architecture Name>"
    echo
    echo "Usage examples:"
    echo
    echo "    $BUILD_SCRIPT_NAME $CURRENT_LIBRARY_VERSION x86_64-unknown-linux-gnu"
    echo "      -- Installs $LIBRARY_NAME version $CURRENT_LIBRARY_VERSION for x86_64 architecture."
    echo
    echo "    $BUILD_SCRIPT_NAME $CURRENT_LIBRARY_VERSION aarch64-unknown-linux-gnueabi"
    echo "      -- Installs $LIBRARY_NAME version $CURRENT_LIBRARY_VERSION for arm64 architecture."
    echo
    exit 1
}

# Get version and architecture from arguments.
LIBRARY_VERSION=$1
if [ -z "$LIBRARY_VERSION" ]
then
    UsageAndExit
fi

ARCH_NAME=$2
if [ -z "$ARCH_NAME" ]
then
    UsageAndExit
fi

UE_MODULE_LOCATION=$BUILD_SCRIPT_DIR
UE_SOURCE_THIRD_PARTY_LOCATION=`cd $UE_MODULE_LOCATION/..; pwd`
UE_ENGINE_LOCATION=`cd $UE_SOURCE_THIRD_PARTY_LOCATION/../..; pwd`

SOURCE_LOCATION="$UE_MODULE_LOCATION/$REPOSITORY_NAME-$LIBRARY_VERSION"

BUILD_LOCATION="$UE_MODULE_LOCATION/Intermediate"

# Specify all of the include/bin/lib directory variables so that CMake can
# compute relative paths correctly for the imported targets.
INSTALL_INCLUDEDIR=include
INSTALL_BIN_DIR="Unix/$ARCH_NAME/bin"
INSTALL_LIB_DIR="Unix/$ARCH_NAME/lib"

INSTALL_LOCATION="$UE_MODULE_LOCATION/Deploy/$REPOSITORY_NAME-$LIBRARY_VERSION"
INSTALL_INCLUDE_LOCATION="$INSTALL_LOCATION/$INSTALL_INCLUDEDIR"
INSTALL_UNIX_ARCH_LOCATION="$INSTALL_LOCATION/Unix/$ARCH_NAME"

rm -rf $BUILD_LOCATION
rm -rf $INSTALL_INCLUDE_LOCATION
rm -rf $INSTALL_UNIX_ARCH_LOCATION

mkdir $BUILD_LOCATION
pushd $BUILD_LOCATION > /dev/null

# Run Engine/Build/BatchFiles/Linux/SetupToolchain.sh first to ensure
# that the toolchain is setup and verify that this name matches.
TOOLCHAIN_NAME=v24_clang-19.1.7-rockylinux8

UE_TOOLCHAIN_LOCATION="$UE_ENGINE_LOCATION/Extras/ThirdPartyNotUE/SDKs/HostLinux/Linux_x64/$TOOLCHAIN_NAME"

C_FLAGS=""
CXX_FLAGS="-nostdinc++ -I$UE_SOURCE_THIRD_PARTY_LOCATION/Unix/LibCxx/include/c++/v1"
LINKER_FLAGS="-nodefaultlibs -stdlib=libc++ -L$UE_SOURCE_THIRD_PARTY_LOCATION/Unix/LibCxx/lib/Unix/$ARCH_NAME/ -lc++ -lc++abi -lm -lc -lgcc_s -lgcc"

# Determine whether we're cross compiling for an architecture that doesn't
# match the host. This is the way that CMake determines the value for the
# CMAKE_HOST_SYSTEM_PROCESSOR variable.
HOST_SYSTEM_PROCESSOR=`uname -m`
TARGET_SYSTEM_PROCESSOR=$HOST_SYSTEM_PROCESSOR

if [[ $ARCH_NAME != $HOST_SYSTEM_PROCESSOR* ]]
then
    ARCH_NAME_PARTS=(${ARCH_NAME//-/ })
    TARGET_SYSTEM_PROCESSOR=${ARCH_NAME_PARTS[0]}
fi

( cat <<_EOF_
    # Auto-generated by script: $BUILD_SCRIPT_DIR/$BUILD_SCRIPT_NAME

    message (STATUS "UE_TOOLCHAIN_LOCATION is '${UE_TOOLCHAIN_LOCATION}'")
    message (STATUS "ARCH_NAME is '${ARCH_NAME}'")
    message (STATUS "TARGET_SYSTEM_PROCESSOR is '${TARGET_SYSTEM_PROCESSOR}'")

    set(CMAKE_SYSTEM_NAME Linux)
    set(CMAKE_SYSTEM_PROCESSOR ${TARGET_SYSTEM_PROCESSOR})

    set(CMAKE_SYSROOT ${UE_TOOLCHAIN_LOCATION}/${ARCH_NAME})
    set(CMAKE_LIBRARY_ARCHITECTURE ${ARCH_NAME})

    set(CMAKE_C_COMPILER \${CMAKE_SYSROOT}/bin/clang)
    set(CMAKE_C_COMPILER_TARGET ${ARCH_NAME})
    set(CMAKE_C_FLAGS "-target ${ARCH_NAME} ${C_FLAGS}")

    set(CMAKE_CXX_COMPILER \${CMAKE_SYSROOT}/bin/clang++)
    set(CMAKE_CXX_COMPILER_TARGET ${ARCH_NAME})
    set(CMAKE_CXX_FLAGS "-target ${ARCH_NAME} ${CXX_FLAGS}")

    set(CMAKE_EXE_LINKER_FLAGS "${LINKER_FLAGS}")
    set(CMAKE_MODULE_LINKER_FLAGS "${LINKER_FLAGS}")
    set(CMAKE_SHARED_LINKER_FLAGS "${LINKER_FLAGS}")

    set(CMAKE_FIND_ROOT_PATH "${UE_TOOLCHAIN_LOCATION}")
    set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
    set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
    set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
    set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)
_EOF_
) > /tmp/__cmake_toolchain.cmake

CMAKE_ARGS=(
    -DCMAKE_TOOLCHAIN_FILE="/tmp/__cmake_toolchain.cmake"
    -DCMAKE_INSTALL_PREFIX="$INSTALL_LOCATION"
    -DCMAKE_INSTALL_INCLUDEDIR="$INSTALL_INCLUDEDIR"
    -DCMAKE_INSTALL_BINDIR="$INSTALL_BIN_DIR"
    -DCMAKE_INSTALL_LIBDIR="$INSTALL_LIB_DIR"
    -DCMAKE_POSITION_INDEPENDENT_CODE=ON
    -DPUGIXML_USE_POSTFIX=ON
)

NUM_CPU=`grep -c ^processor /proc/cpuinfo`

echo Configuring Debug build for $LIBRARY_NAME version $LIBRARY_VERSION...
cmake -G "Unix Makefiles" $SOURCE_LOCATION -DCMAKE_BUILD_TYPE=Debug "${CMAKE_ARGS[@]}"

echo Building $LIBRARY_NAME for Debug...
cmake --build . -j$NUM_CPU

echo Installing $LIBRARY_NAME for Debug...
cmake --install .

# The Unix Makefiles generator does not support multiple configurations, so we
# need to re-configure for Release.
echo Configuring Release build for $LIBRARY_NAME version $LIBRARY_VERSION...
cmake -G "Unix Makefiles" $SOURCE_LOCATION -DCMAKE_BUILD_TYPE=Release "${CMAKE_ARGS[@]}"

echo Building $LIBRARY_NAME for Release...
cmake --build . -j$NUM_CPU

echo Installing $LIBRARY_NAME for Release...
cmake --install .

popd > /dev/null

echo Removing pkgconfig files...
rm -f "$INSTALL_LOCATION/$INSTALL_LIB_DIR/pkgconfig/pugixml.pc"
rmdir "$INSTALL_LOCATION/$INSTALL_LIB_DIR/pkgconfig"

echo Done.
