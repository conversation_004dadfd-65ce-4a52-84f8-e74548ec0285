<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>libjpeg-turbo</Name>
  <Location>/Engine/Source/ThirdParty/libjpeg-turbo</Location>
  <Function>A SIMD optimized JPEG encode/decode library with up to 6x faster encode performance over the default solution in UE. It's currently used for RemoteSession editor and game streaming to mobile devices and it's used heavily in UE4 Virtual Cameras in Virtual Production.</Function>
  <Eula>https://github.com/libjpeg-turbo/libjpeg-turbo/blob/master/LICENSE.md</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>