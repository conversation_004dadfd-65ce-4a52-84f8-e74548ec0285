<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ButtonAutoSync" xml:space="preserve">
    <value>Toggle Auto Sync</value>
  </data>
  <data name="ButtonAutoSyncHint" xml:space="preserve">
    <value>Toggle Direct Link Auto Sync</value>
  </data>
  <data name="ButtonConnections" xml:space="preserve">
    <value>Connections</value>
  </data>
  <data name="ButtonConnectionsHint" xml:space="preserve">
    <value>Manage Connections</value>
  </data>
  <data name="ButtonExport3DView" xml:space="preserve">
    <value>Export to Datasmith file</value>
  </data>
  <data name="ButtonExport3DViewHint" xml:space="preserve">
    <value>Export to Datasmith file</value>
  </data>
  <data name="ButtonMessages" xml:space="preserve">
    <value>Messages</value>
  </data>
  <data name="ButtonMessagesHint" xml:space="preserve">
    <value>Show Messages</value>
  </data>
  <data name="ButtonSettings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="ButtonSettingsHint" xml:space="preserve">
    <value>Show Settings</value>
  </data>
  <data name="ButtonSync" xml:space="preserve">
    <value>Synchronize</value>
  </data>
  <data name="ButtonSyncHint" xml:space="preserve">
    <value>Synchronize with Direct Link</value>
  </data>
  <data name="DatasmithTabName" xml:space="preserve">
    <value>Datasmith</value>
  </data>
  <data name="ExportOptionsDialog_ButtonOK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ExportOptionsDialog_MessageNoViewsSelected" xml:space="preserve">
    <value>No views selected for export! Please select at least one 3D view.</value>
  </data>
  <data name="ExportOptionsDialog_Select3DViews" xml:space="preserve">
    <value>Select 3D Views</value>
  </data>
  <data name="ExportOptionsDialog_Title" xml:space="preserve">
    <value>Unreal Datasmith Export - Debug Options</value>
  </data>
  <data name="ExportOptionsDialog_WriteLogFile" xml:space="preserve">
    <value>Write Log File</value>
  </data>
  <data name="ExportOptionsDialog_WriteLogFileTooltip" xml:space="preserve">
    <value>Write a '.log' file aside the '.udatasmith' file.\nThis log file records some details about the exported Revit elements.\nThe log file can become quite big for complex 3D views.</value>
  </data>
  <data name="MessagesDialog_ButtonClear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="MessagesDialog_ButtonClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="MessagesDialog_Title" xml:space="preserve">
    <value>Unreal Datasmith Export - Messages</value>
  </data>
  <data name="RibbonSection_Datasmith" xml:space="preserve">
    <value>Datasmith</value>
  </data>
  <data name="RibbonSection_DirectLink" xml:space="preserve">
    <value>Direct Link</value>
  </data>
  <data name="RibbonSection_FileExport" xml:space="preserve">
    <value>File Export</value>
  </data>
  <data name="SettingsDialog_AddGroupsDialogTitle" xml:space="preserve">
    <value>Add Groups</value>
  </data>
  <data name="SettingsDialog_ButtonAddGroups" xml:space="preserve">
    <value>Add Group...</value>
  </data>
  <data name="SettingsDialog_ButtonClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="SettingsDialog_ButtonRemoveGroups" xml:space="preserve">
    <value>Remove Selected</value>
  </data>
  <data name="SettingsDialog_DialogTitle" xml:space="preserve">
    <value>Datasmith Export Settings</value>
  </data>
  <data name="SettingsDialog_InsertionPoint" xml:space="preserve">
    <value>Insertion Point</value>
  </data>
  <data name="SettingsDialog_InsertionPointTooltip" xml:space="preserve">
    <value>Change world origin for all exported elements</value>
  </data>
  <data name="SettingsDialog_LabelMatchGroups" xml:space="preserve">
    <value>Metadata Export Filter</value>
  </data>
  <data name="SettingsDialog_LabelMatchParamNames" xml:space="preserve">
    <value>Match Parameter Names</value>
  </data>
  <data name="SettingsDialog_LabelMetadataDescription" xml:space="preserve">
    <value>Specify parameters (medata) export filter that will be used during DirectLink synchronization. Only parameters that match the filter will be exported as metadata.</value>
  </data>
  <data name="SettingsDialog_LevelOfTesselation" xml:space="preserve">
    <value>Level of Tessellation</value>
  </data>
  <data name="SettingsDialog_LevelOfTesselationTooltip" xml:space="preserve">
    <value>1 to 15 : Revit will use the suggested level of detail when tessellating faces.\nUsing a value close to the middle of the range yields a very reasonable tessellation.\nRevit uses level 8 as its 'normal' level of detail.</value>
  </data>
  <data name="SettingsDialog_MetadataFilterTooltip" xml:space="preserve">
    <value>Filter what metadata to export by adding allowed paramter groups.</value>
  </data>
</root>