// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeLevelSequence : FDatasmithFacadeElement {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeLevelSequence(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeLevelSequence obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeLevelSequence(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  public FDatasmithFacadeLevelSequence(string InName) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeLevelSequence(InName), true) {
  }

  public string GetFile() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_GetFile(swigCPtr));
    return ret;
  }

  public void SetFile(string InFile) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_SetFile(swigCPtr, InFile);
  }

  private void InternalGetFileHash(System.Text.StringBuilder OutBuffer, ulong BufferSize) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_InternalGetFileHash(swigCPtr, OutBuffer, BufferSize);
  }

  public void SetFileHash(string Hash) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_SetFileHash(swigCPtr, Hash);
  }

  public float GetFrameRate() {
    float ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_GetFrameRate(swigCPtr);
    return ret;
  }

  public void SetFrameRate(float FramePerSecs) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_SetFrameRate(swigCPtr, FramePerSecs);
  }

  public void AddAnimation(FDatasmithFacadeTransformAnimation InAnimation) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_AddAnimation(swigCPtr, FDatasmithFacadeTransformAnimation.getCPtr(InAnimation));
  }

  public int GetAnimationsCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_GetAnimationsCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeTransformAnimation GetTransformAnimation(int InIndex) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_GetTransformAnimation(swigCPtr, InIndex);
    FDatasmithFacadeTransformAnimation ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeTransformAnimation(cPtr, false);
    return ret;
  }

  public FDatasmithFacadeVisibilityAnimation GetVisibilityAnimation(int InIndex) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_GetVisibilityAnimation(swigCPtr, InIndex);
    FDatasmithFacadeVisibilityAnimation ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeVisibilityAnimation(cPtr, false);
    return ret;
  }

  public FDatasmithFacadeSubsequenceAnimation GetSubsequenceAnimation(int InIndex) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_GetSubsequenceAnimation(swigCPtr, InIndex);
    FDatasmithFacadeSubsequenceAnimation ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeSubsequenceAnimation(cPtr, false);
    return ret;
  }

  public void RemoveAnimation(FDatasmithFacadeBaseAnimation InAnimation) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeLevelSequence_RemoveAnimation(swigCPtr, FDatasmithFacadeBaseAnimation.getCPtr(InAnimation));
  }

}
