// Copyright Epic Games, Inc. All Rights Reserved.

#include "SBuildLogin.h"
#include "Internationalization/FastDecimalFormat.h"
#include "Math/BasicMathExpressionEvaluator.h"
#include "Math/UnitConversion.h"
#include "Misc/ExpressionParser.h"
#include "Styling/StyleColors.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SHyperlink.h"
#include "Widgets/Layout/SGridPanel.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Views/SListView.h"
#include "ZenServiceInstanceManager.h"

#define LOCTEXT_NAMESPACE "StorageServerBuild"

void SBuildLogin::Construct(const FArguments& InArgs)
{
	ZenServiceInstance = InArgs._ZenServiceInstance;
	BuildServiceInstance = InArgs._BuildServiceInstance;

	this->ChildSlot
	[
		SNew(SVerticalBox)

		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(0, 0, 0, 0)
		.Expose(GridSlot)
		[
			GetGridPanel()
		]
	];
}

TSharedRef<SWidget> SBuildLogin::GetGridPanel()
{
	TSharedRef<SGridPanel> Panel = SNew(SGridPanel);

	const float RowMargin = 0.0f;
	const float ColumnMargin = 10.0f;
	const FSlateColor TitleColor = FStyleColors::AccentWhite;
	const FSlateFontInfo TitleFont = FCoreStyle::GetDefaultFontStyle("Bold", 10);

	Panel->AddSlot(0, 0)
	[
		SNew(STextBlock)
		.Margin(FMargin(ColumnMargin, RowMargin))
		.ColorAndOpacity(TitleColor)
		.Font(TitleFont)
		.Text(LOCTEXT("BuildConnect_StatusLabel", "Status:"))
	];

	Panel->AddSlot(1, 0)
	[
		SNew(STextBlock)
		.Text_Lambda([this]
		{
			if (TSharedPtr<UE::Zen::Build::FBuildServiceInstance> ServiceInstance = BuildServiceInstance.Get())
			{
				switch (ServiceInstance->GetConnectionState())
				{
				case UE::Zen::Build::FBuildServiceInstance::EConnectionState::NotStarted:
					return LOCTEXT("BuildConnect_StatusValueNotConnected", "Not Connected");
				case UE::Zen::Build::FBuildServiceInstance::EConnectionState::ConnectionInProgress:
					return LOCTEXT("BuildConnect_StatusValueConnecting", "Connecting...");
				case UE::Zen::Build::FBuildServiceInstance::EConnectionState::ConnectionSucceeded:
					{
						return FText::Format(LOCTEXT("BuildConnect_StatusValueConnected", "Connected to {0}"),
							FText::FromStringView(ServiceInstance->GetEffectiveDomain()));
					}
				case UE::Zen::Build::FBuildServiceInstance::EConnectionState::ConnectionFailed:
					return LOCTEXT("BuildConnect_StatusValueConnectionFailed", "Connection failed");
				}
			}
			return LOCTEXT("BuildConnect_StatusValueError", "Error");
		})
		.Visibility_Lambda([this]
		{
			if (TSharedPtr<UE::Zen::Build::FBuildServiceInstance> ServiceInstance = BuildServiceInstance.Get())
			{
				if (ServiceInstance->GetConnectionState() == UE::Zen::Build::FBuildServiceInstance::EConnectionState::NotStarted)
				{
					return EVisibility::Collapsed;
				}
			}
			return EVisibility::Visible;
		})
	];

	Panel->AddSlot(2, 0)
	[
		SNew(SHyperlink)
		.Padding(FMargin(ColumnMargin, RowMargin))
		.UnderlineStyle(FAppStyle::Get(), "NoBorder")
		.Text_Lambda([this]
		{
			FText BrowseText = LOCTEXT("BuildLogin_ConnectLink", "Click to connect");
			return BrowseText;
		})
		.OnNavigate_Lambda([this]
		{
			if (TSharedPtr<UE::Zen::Build::FBuildServiceInstance> ServiceInstance = BuildServiceInstance.Get())
			{
				ServiceInstance->Connect([ServiceInstance](UE::Zen::Build::FBuildServiceInstance::EConnectionState ConnectionState)
					{
						if (ConnectionState == UE::Zen::Build::FBuildServiceInstance::EConnectionState::ConnectionSucceeded)
						{
							ServiceInstance->RefreshNamespacesAndBuckets();
						}
					});
			}
		})
		.Visibility_Lambda([this]
		{
			if (TSharedPtr<UE::Zen::Build::FBuildServiceInstance> ServiceInstance = BuildServiceInstance.Get())
			{
				if (ServiceInstance->GetConnectionState() == UE::Zen::Build::FBuildServiceInstance::EConnectionState::NotStarted)
				{
					return EVisibility::Visible;
				}
			}
			return EVisibility::Hidden;
		})
	];


	return Panel;
}

#undef LOCTEXT_NAMESPACE
