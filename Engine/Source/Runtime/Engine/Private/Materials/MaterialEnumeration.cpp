// Copyright Epic Games, Inc. All Rights Reserved.

#include "Materials/MaterialEnumeration.h"

int32 IMaterialEnumerationProvider::GetValueOrDefault(FName EntryName, int32 DefaultValue) const
{
	int32 Value;
	ResolveValue(EntryName, Value, DefaultValue);
	return Value;
}

bool UMaterialEnumeration::ResolveValue(FName EntryName, int32& OutValue, int32 DefaultValue) const
{
	if (SortedEntries.Num() != Entries.Num())
	{
		for (const FMaterialEnumerationEntry& Entry : Entries)
		{
			if (Entry.Name == EntryName)
			{
				OutValue = Entry.Value;
				return true;
			}
		}
	}
	else
	{
		int32 Index = Algo::BinarySearchBy(SortedEntries, EntryName, &FMaterialEnumerationEntry::Name, FNameFastLess{});
		if (Index != INDEX_NONE)
		{
			OutValue = SortedEntries[Index].Value;
			return true;
		}
	}
	OutValue = DefaultValue;
	return false;
}

void UMaterialEnumeration::ForEachEntry(TFunctionRef<void (FName Name, int32 Value)> Iterator) const
{
	for (const FMaterialEnumerationEntry& Entry : Entries)
	{
		Iterator(Entry.Name, Entry.Value);
	}
}

void UMaterialEnumeration::SortEntries()
{
	SortedEntries = Entries;
	SortedEntries.Sort([](const FMaterialEnumerationEntry& A, const FMaterialEnumerationEntry& B)
	{
		return A.Name.FastLess(B.Name);
	});
}

void UMaterialEnumeration::PostLoad()
{
	Super::PostLoad();
	SortEntries();
}

#if WITH_EDITOR

void UMaterialEnumeration::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);
	SortEntries();
}

#endif
