// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once

#define EYE_ADAPTATION_LOOSE_PARAMETERS 1
#include "../EyeAdaptationCommon.ush"
#include "../TonemapCommon.ush"
#include "../ShaderPrint.ush"
#include "LumenReflectionsCombine.ush"
#include "LumenMaterial.ush"

uint VisualizeHiResSurface;
uint2 InputViewSize;
uint2 InputViewOffset;
uint2 OutputViewSize;
uint2 OutputViewOffset;
int Tonemap;

#define LUT_SCALING	1.05f
#define LUT_SIZE	32.f
Texture3D ColorGradingLUT;
SamplerState ColorGradingLUTSampler;

float3 ColorLookupTable(float3 LinearColor)
{
	//@todo - reuse with PostProcessTonemap.usf and handle OutputDevice
	float3 LUTEncodedColor = LinToLog( LinearColor + LogToLin( 0 ) );
	float3 UVW = LUTEncodedColor * ((LUT_SIZE - 1) / LUT_SIZE) + (0.5f / LUT_SIZE);
	return ColorGradingLUT.SampleLevel(ColorGradingLUTSampler, UVW, 0).rgb * LUT_SCALING;
}

float3 VisualizeTonemap(float3 LinearColor)
{
	float3 OutColor = LinearColor;

	// The overview outputs after the tonemapper to overwrite editor primitives and must apply its own tonemapping
	if (Tonemap > 0)
	{
		OutColor = ColorLookupTable(LinearColor * View.OneOverPreExposure * EyeAdaptationLookup());
	}
	else
	{
		OutColor = LinearToSrgbBranchless(LinearColor);
	}

	return OutColor;
}

/**
 * Common Lumen Visualization part, shared between SWRT and HWRT visualization shaders.
 */
void LumenVisualizationFinalize(
	uint2 OutputScreenCoord,
	float2 ViewportUV,
	float3 CameraVector,
	inout float3 FinalColor)
{
#if ENABLE_VISUALIZE_MODE
	const int2 DebugScreenCoord = View.CursorPosition.x >= 0 ? View.CursorPosition : View.BufferSizeAndInvSize.xy / 2;
	FShaderPrintContext DebugContext = InitShaderPrintContext(all(OutputScreenCoord == DebugScreenCoord), float2(0.6, 0.1));
	if (DebugContext.bIsActive && VisualizeMode == VISUALIZE_MODE_ALBEDO)
	{
		Print(DebugContext, TEXT("LinearDiffuseColor: "));
		Print(DebugContext, FinalColor);
	}

	if (VisualizeMode == VISUALIZE_MODE_ALBEDO)
	{
		FinalColor = LinearToSrgbBranchless(FinalColor);
	}
	else if (VisualizeMode == VISUALIZE_MODE_NORMALS
			|| VisualizeMode == VISUALIZE_MODE_GEOMETRY_NORMALS
			|| VisualizeMode == VISUALIZE_MODE_OPACITY)
	{
		// Don't apply SRGB or tonemapping to material attribute visualizations
	}
	else
	{
		// The overview outputs after the tonemapper to overwrite editor primitives and must apply its own tonemapping
		FinalColor = VisualizeTonemap(FinalColor);
	}

	if (VisualizeMode == VISUALIZE_MODE_DEDICATED_REFLECTION_RAYS)
	{
		const float2 GBufferUV = (ViewportUV * View.ViewRectMinAndSize.zw + 0.5f) * View.BufferSizeAndInvSize.zw;
		const uint2 PixelPos = ViewportUV * View.BufferSizeAndInvSize.xy;
		const FLumenMaterialData Material = ReadMaterialDataFromSceneTextures(PixelPos, GBufferUV);
		FinalColor.rgb = Luminance(sqrt(Material.DiffuseAlbedo));
			
		// Mark pixels which require dedicated reflection rays
		if (LumenCombineReflectionsAlpha(Material.Roughness, Material.bHasBackfaceDiffuse) > 0.0f ||  IsClearCoat(Material))
		{
			FinalColor.rgb = Material.bHasBackfaceDiffuse ? float3(0, 1, 0) : float3(1, 0, 0);
			FinalColor.rgb *= (1.0f - Material.Roughness) * 0.8f + 0.2f;
		}
		
		// Add sky as a background
		if (Material.SceneDepth > 1e6)
		{
			FinalColor.rgb = VisualizeTonemap(EvaluateSkyRadiance(CameraVector));
		}
	}
#endif
}