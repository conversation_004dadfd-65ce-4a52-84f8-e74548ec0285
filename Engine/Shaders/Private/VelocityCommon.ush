// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
	VelocityCommon.usf: Common functions for calculating velocity vectors.
=============================================================================*/

#pragma once

float3 Calculate3DVelocity(float4 PackedVelocityA, float4 PackedVelocityC)
{
	float2 ScreenPos = PackedVelocityA.xy / PackedVelocityA.w - ResolvedView.TemporalAAJitter.xy;
	float2 PrevScreenPos = PackedVelocityC.xy / PackedVelocityC.w - ResolvedView.TemporalAAJitter.zw;

	float DeviceZ = PackedVelocityA.z / PackedVelocityA.w;
	float PrevDeviceZ = PackedVelocityC.z / PackedVelocityC.w;

	// 3d velocity, includes camera an object motion
	float3 Velocity = float3(ScreenPos - PrevScreenPos, DeviceZ - PrevDeviceZ);

	// Make sure not to touch 0,0 which is clear color
	return Velocity;
}
